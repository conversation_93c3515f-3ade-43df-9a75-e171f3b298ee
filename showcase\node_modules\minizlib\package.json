{"name": "minizlib", "version": "3.0.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "./dist/commonjs/index.js", "dependencies": {"minipass": "^7.1.2"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "MIT", "devDependencies": {"@types/node": "^22.13.14", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.1"}, "files": ["dist"], "engines": {"node": ">= 18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "module": "./dist/esm/index.js"}